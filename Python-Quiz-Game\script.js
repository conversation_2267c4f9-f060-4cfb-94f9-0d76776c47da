// Python Quiz Game - Main JavaScript File
class PythonQuizGame {
    constructor() {
        this.currentQuestionIndex = 0;
        this.questions = [];
        this.userAnswers = [];
        this.score = 0;
        // Timer removed for better learning experience
        this.difficulty = 'beginner';
        this.startTime = null;
        this.questionStartTime = null;
        this.questionTimes = [];
        
        this.initializeElements();
        this.bindEvents();
        this.showScreen('start-screen');
    }

    initializeElements() {
        // Screens
        this.startScreen = document.getElementById('start-screen');
        this.quizScreen = document.getElementById('quiz-screen');
        this.resultsScreen = document.getElementById('results-screen');
        this.reviewScreen = document.getElementById('review-screen');

        // Quiz elements
        this.questionText = document.getElementById('question-text');
        this.questionCode = document.getElementById('question-code');
        this.questionCounter = document.getElementById('question-counter');
        this.scoreDisplay = document.getElementById('score-display');
        // Timer display removed
        this.progressFill = document.getElementById('progress-fill');

        // Answer elements
        this.multipleChoice = document.getElementById('multiple-choice');
        this.codeInput = document.getElementById('code-input');
        this.codeEditor = document.getElementById('code-editor');
        this.codeOutput = document.getElementById('code-output');

        // Buttons
        this.nextButton = document.getElementById('next-question');
        this.skipButton = document.getElementById('skip-question');
        this.runCodeButton = document.getElementById('run-code');
        this.submitCodeButton = document.getElementById('submit-code');
        this.showHintsButton = document.getElementById('show-hints');
        this.showSolutionButton = document.getElementById('show-solution');
        this.hintsContainer = document.getElementById('hints-container');
        this.hintsList = document.getElementById('hints-list');
        this.feedbackContainer = document.getElementById('feedback-container');
        this.feedbackTitle = document.getElementById('feedback-title');
        this.feedbackText = document.getElementById('feedback-text');
    }

    bindEvents() {
        // Difficulty selection
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.difficulty = e.target.dataset.level;
                this.startQuiz();
            });
        });

        // Answer option buttons
        this.multipleChoice.addEventListener('click', (e) => {
            if (e.target.classList.contains('option-btn')) {
                this.selectOption(e.target);
            }
        });

        // Action buttons
        this.nextButton.addEventListener('click', () => this.nextQuestion());
        this.skipButton.addEventListener('click', () => this.skipQuestion());
        this.runCodeButton.addEventListener('click', () => this.runCode());
        this.submitCodeButton.addEventListener('click', () => this.submitCode());
        this.showHintsButton.addEventListener('click', () => this.showHints());
        this.showSolutionButton.addEventListener('click', () => this.showSolution());

        // Results buttons
        document.getElementById('restart-quiz').addEventListener('click', () => this.restartQuiz());
        document.getElementById('review-answers').addEventListener('click', () => this.showReview());
        document.getElementById('share-results').addEventListener('click', () => this.shareResults());
        document.getElementById('back-to-results').addEventListener('click', () => this.showScreen('results-screen'));

        // Code editor events
        this.codeEditor.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = e.target.selectionStart;
                const end = e.target.selectionEnd;
                e.target.value = e.target.value.substring(0, start) + '    ' + e.target.value.substring(end);
                e.target.selectionStart = e.target.selectionEnd = start + 4;
            }
        });
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }

    startQuiz() {
        this.questions = getQuestions(this.difficulty);
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.score = 0;
        this.questionTimes = [];
        this.startTime = Date.now();
        
        this.showScreen('quiz-screen');
        this.displayQuestion();
    }

    displayQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        this.questionStartTime = Date.now();
        
        // Update UI
        this.questionText.textContent = question.question;
        this.questionCounter.textContent = `Vraag ${this.currentQuestionIndex + 1} van ${this.questions.length}`;
        this.scoreDisplay.textContent = `Score: ${this.score}`;
        this.updateProgress();

        // Show/hide code block
        if (question.code) {
            this.questionCode.textContent = question.code;
            this.questionCode.style.display = 'block';
        } else {
            this.questionCode.style.display = 'none';
        }

        // Display appropriate answer interface
        if (question.type === 'multiple-choice') {
            this.showMultipleChoice(question);
        } else if (question.type === 'code-input') {
            this.showCodeInput(question);
        }

        // Reset feedback containers
        this.hintsContainer.style.display = 'none';
        this.feedbackContainer.style.display = 'none';
    }

    showMultipleChoice(question) {
        this.multipleChoice.style.display = 'grid';
        this.codeInput.style.display = 'none';
        
        const optionButtons = this.multipleChoice.querySelectorAll('.option-btn');
        optionButtons.forEach((btn, index) => {
            const optionKey = String.fromCharCode(65 + index); // A, B, C, D
            btn.textContent = `${optionKey}. ${question.options[optionKey]}`;
            btn.dataset.option = optionKey;
            btn.classList.remove('selected', 'correct', 'incorrect');
        });

        this.nextButton.style.display = 'none';
    }

    showCodeInput(question) {
        this.multipleChoice.style.display = 'none';
        this.codeInput.style.display = 'block';
        
        this.codeEditor.value = '';
        this.codeOutput.textContent = '';
        this.codeOutput.className = 'code-output';
        
        this.nextButton.style.display = 'none';
    }

    selectOption(button) {
        // Remove previous selections
        this.multipleChoice.querySelectorAll('.option-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        // Select current option
        button.classList.add('selected');
        
        // Show next button
        this.nextButton.style.display = 'inline-block';
        
        // Auto-advance after short delay
        setTimeout(() => {
            if (button.classList.contains('selected')) {
                this.submitAnswer(button.dataset.option);
            }
        }, 1000);
    }

    runCode() {
        const code = this.codeEditor.value.trim();
        if (!code) {
            this.codeOutput.textContent = 'Voer eerst code in om uit te voeren.';
            this.codeOutput.className = 'code-output error';
            return;
        }

        try {
            // Simple Python code simulation for common cases
            const result = this.simulatePythonExecution(code);
            this.codeOutput.textContent = result;
            this.codeOutput.className = 'code-output success';
        } catch (error) {
            this.codeOutput.textContent = `Error: ${error.message}`;
            this.codeOutput.className = 'code-output error';
        }
    }

    simulatePythonExecution(code) {
        // Simple simulation of Python code execution
        // This is a basic implementation for educational purposes
        
        // Handle print statements
        const printMatches = code.match(/print\((.*?)\)/g);
        if (printMatches) {
            let output = '';
            printMatches.forEach(match => {
                const content = match.match(/print\((.*?)\)/)[1];
                // Simple evaluation for basic cases
                if (content.includes('"') || content.includes("'")) {
                    // String literal
                    output += content.replace(/['"]/g, '') + '\n';
                } else if (!isNaN(content)) {
                    // Number
                    output += content + '\n';
                } else {
                    // Variable or expression
                    output += `${content}\n`;
                }
            });
            return output.trim();
        }

        // Handle simple expressions
        if (code.includes('range(')) {
            return 'Code uitgevoerd - controleer je logica';
        }

        return 'Code uitgevoerd - resultaat kan variëren';
    }

    submitCode() {
        const code = this.codeEditor.value.trim();
        this.submitAnswer(code);
    }

    showHints() {
        const question = this.questions[this.currentQuestionIndex];
        if (question.explanation && question.explanation.hints) {
            this.hintsList.innerHTML = '';
            question.explanation.hints.forEach(hint => {
                const li = document.createElement('li');
                li.textContent = hint;
                this.hintsList.appendChild(li);
            });
            this.hintsContainer.style.display = 'block';
        }
    }

    showSolution() {
        const question = this.questions[this.currentQuestionIndex];
        if (question.solution) {
            this.codeEditor.value = question.solution;
            this.showFeedback(true, question.explanation.correct);
        }
    }

    showFeedback(isCorrect, message) {
        this.feedbackContainer.className = `feedback-container ${isCorrect ? 'correct' : 'incorrect'}`;
        this.feedbackTitle.textContent = isCorrect ? '✅ Correct!' : '❌ Niet helemaal juist';
        this.feedbackText.textContent = message;
        this.feedbackContainer.style.display = 'block';
    }

    submitAnswer(answer) {
        const question = this.questions[this.currentQuestionIndex];
        const timeSpent = Date.now() - this.questionStartTime;
        this.questionTimes.push(timeSpent);

        const userAnswer = {
            questionId: question.id,
            question: question.question,
            userAnswer: answer,
            correctAnswer: question.correct || question.solution,
            isCorrect: this.checkAnswer(question, answer),
            timeSpent: timeSpent,
            category: question.category
        };

        this.userAnswers.push(userAnswer);

        if (userAnswer.isCorrect) {
            this.score++;
        }

        // Show feedback for multiple choice
        if (question.type === 'multiple-choice') {
            this.showMultipleChoiceFeedback(question, answer);
        }

        // Move to next question after delay
        setTimeout(() => {
            this.nextQuestion();
        }, question.type === 'multiple-choice' ? 2000 : 500);
    }

    checkAnswer(question, answer) {
        if (question.type === 'code-input') {
            const userCode = answer.toLowerCase().replace(/\s+/g, ' ').trim();

            // Check if user code contains expected keywords
            if (question.expectedKeywords) {
                const foundKeywords = question.expectedKeywords.filter(keyword =>
                    userCode.includes(keyword.toLowerCase())
                );

                // Consider correct if most keywords are present
                const correctnessRatio = foundKeywords.length / question.expectedKeywords.length;
                const isCorrect = correctnessRatio >= 0.7; // 70% of keywords must be present

                // Show appropriate feedback
                const message = isCorrect ?
                    question.explanation.correct :
                    question.explanation.incorrect;

                this.showFeedback(isCorrect, message);

                return isCorrect;
            }
        }
        return false;
    }

    showMultipleChoiceFeedback(question, selectedAnswer) {
        const optionButtons = this.multipleChoice.querySelectorAll('.option-btn');
        optionButtons.forEach(btn => {
            if (btn.dataset.option === question.correct) {
                btn.classList.add('correct');
            } else if (btn.dataset.option === selectedAnswer && selectedAnswer !== question.correct) {
                btn.classList.add('incorrect');
            }
        });
    }

    skipQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        const timeSpent = Date.now() - this.questionStartTime;
        this.questionTimes.push(timeSpent);

        const userAnswer = {
            questionId: question.id,
            question: question.question,
            userAnswer: 'Overgeslagen',
            correctAnswer: question.correct || question.solution,
            isCorrect: false,
            timeSpent: timeSpent,
            category: question.category,
            skipped: true
        };

        this.userAnswers.push(userAnswer);
        this.nextQuestion();
    }

    nextQuestion() {
        this.currentQuestionIndex++;
        
        if (this.currentQuestionIndex >= this.questions.length) {
            this.endQuiz();
        } else {
            this.displayQuestion();
        }
    }

    updateProgress() {
        const progress = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
        this.progressFill.style.width = `${progress}%`;
    }

    endQuiz() {
        this.showResults();
        this.showScreen('results-screen');
    }

    showResults() {
        const totalQuestions = this.questions.length;
        const correctAnswers = this.userAnswers.filter(a => a.isCorrect).length;
        const wrongAnswers = this.userAnswers.filter(a => !a.isCorrect && !a.skipped).length;
        const skippedAnswers = this.userAnswers.filter(a => a.skipped).length;
        const percentage = Math.round((correctAnswers / totalQuestions) * 100);
        const avgTime = Math.round(this.questionTimes.reduce((a, b) => a + b, 0) / this.questionTimes.length / 1000);

        // Update results display
        document.getElementById('final-score').textContent = correctAnswers;
        document.querySelector('.score-total').textContent = `/ ${totalQuestions}`;
        document.getElementById('score-percentage').textContent = `${percentage}%`;
        document.getElementById('correct-answers').textContent = correctAnswers;
        document.getElementById('wrong-answers').textContent = wrongAnswers;
        document.getElementById('skipped-answers').textContent = skippedAnswers;
        document.getElementById('avg-time').textContent = `${avgTime}s`;

        // Generate study recommendations
        this.generateStudyRecommendations();
    }

    generateStudyRecommendations() {
        const wrongCategories = this.userAnswers
            .filter(a => !a.isCorrect)
            .map(a => a.category)
            .filter((category, index, self) => self.indexOf(category) === index);

        const recommendations = getStudyRecommendations(wrongCategories);
        const recommendationsList = document.getElementById('study-recommendations');
        
        recommendationsList.innerHTML = '';
        if (recommendations.length === 0) {
            recommendationsList.innerHTML = '<li>Geweldig! Je hebt alle vragen goed beantwoord!</li>';
        } else {
            recommendations.forEach(rec => {
                const li = document.createElement('li');
                li.textContent = rec;
                recommendationsList.appendChild(li);
            });
        }
    }

    showReview() {
        const reviewContent = document.getElementById('review-content');
        reviewContent.innerHTML = '';

        this.userAnswers.forEach((answer, index) => {
            const reviewItem = document.createElement('div');
            reviewItem.className = `review-item ${answer.isCorrect ? 'correct' : (answer.skipped ? 'skipped' : 'incorrect')}`;
            
            reviewItem.innerHTML = `
                <div class="review-question">
                    <strong>Vraag ${index + 1}:</strong> ${answer.question}
                </div>
                <div class="review-answer user">
                    <strong>Jouw antwoord:</strong> ${answer.userAnswer}
                </div>
                <div class="review-answer correct">
                    <strong>Correct antwoord:</strong> ${answer.correctAnswer}
                </div>
                ${answer.timeSpent ? `<div class="review-time">Tijd: ${Math.round(answer.timeSpent / 1000)}s</div>` : ''}
            `;
            
            reviewContent.appendChild(reviewItem);
        });

        this.showScreen('review-screen');
    }

    shareResults() {
        const totalQuestions = this.questions.length;
        const correctAnswers = this.userAnswers.filter(a => a.isCorrect).length;
        const percentage = Math.round((correctAnswers / totalQuestions) * 100);
        
        const shareText = `Ik heb zojuist de Python Quiz voltooid! 🐍\nScore: ${correctAnswers}/${totalQuestions} (${percentage}%)\nNiveau: ${this.difficulty}\n\nTest je eigen Python kennis: ${window.location.href}`;
        
        if (navigator.share) {
            navigator.share({
                title: 'Python Quiz Resultaten',
                text: shareText,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareText).then(() => {
                alert('Resultaten gekopieerd naar klembord!');
            });
        }
    }

    restartQuiz() {
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.score = 0;
        this.questionTimes = [];
        this.showScreen('start-screen');
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new PythonQuizGame();
});
