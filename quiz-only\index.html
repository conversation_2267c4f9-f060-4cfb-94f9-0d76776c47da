<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Americaps Projects - Python Quiz Game</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .quiz-btn {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            text-decoration: none;
            border-radius: 15px;
            font-size: 1.3rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .quiz-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        .features {
            margin-top: 40px;
            text-align: left;
        }
        .features h3 {
            margin-bottom: 15px;
            color: #e2e8f0;
        }
        .features ul {
            list-style: none;
            padding: 0;
        }
        .features li {
            margin: 10px 0;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border-left: 4px solid #48bb78;
        }
        .features li::before {
            content: "✓ ";
            color: #48bb78;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐍 Python Quiz Game</h1>
        <p>Test je Python kennis en bereid je voor op je examen met onze interactieve quiz!</p>
        
        <a href="Python-Quiz-Game/index.html" class="quiz-btn">Start de Quiz</a>
        
        <div class="features">
            <h3>Features:</h3>
            <ul>
                <li>3 moeilijkheidsgraden (Beginner, Gemiddeld, Gevorderd)</li>
                <li>Interactieve code editor voor Python</li>
                <li>Real-time feedback en scoring</li>
                <li>Gepersonaliseerde studieaanbevelingen</li>
                <li>Review mode om je antwoorden te bekijken</li>
                <li>Timer per vraag voor extra uitdaging</li>
                <li>Responsive design voor alle apparaten</li>
            </ul>
        </div>
    </div>
</body>
</html>
