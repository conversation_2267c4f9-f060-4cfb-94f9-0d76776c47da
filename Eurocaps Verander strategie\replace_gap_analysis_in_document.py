#!/usr/bin/env python3
"""
Script om correcte gap-analyse grafiek in document te plaatsen
"""

from docx import Document
from docx.shared import Inches
import os

def replace_gap_analysis_in_document():
    """Vervangt gap-analyse grafiek in document met correcte versie"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_PERFECT_CLEAN.docx')
    
    print("Bezig met vervangen gap-analyse grafiek...")
    
    # Zoek "Figuur 2.2: Hofstede Gap-analyse Euro Caps"
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if "Figuur 2.2: Hofstede Gap-analyse Euro Caps" in text:
            print(f"Gevonden Hofstede figuur label op regel {i}")
            
            # Zoek de afbeelding in de volgende paragrafen
            for j in range(i, min(i + 5, len(doc.paragraphs))):
                for run in doc.paragraphs[j].runs:
                    if run._element.xpath('.//a:blip'):  # Heeft afbeelding
                        print(f"Gevonden afbeelding bij Hofstede op regel {j}")
                        
                        # Verwijder oude afbeelding
                        for element in run._element:
                            if element.tag.endswith('drawing'):
                                run._element.remove(element)
                                print("Oude gap-analyse afbeelding verwijderd")
                        
                        # Voeg correcte afbeelding toe
                        try:
                            if os.path.exists('hofstede_gap_analysis_correct.png'):
                                run.add_picture('hofstede_gap_analysis_correct.png', width=Inches(7))
                                print("✅ Correcte Hofstede gap-analyse grafiek ingevoegd")
                            else:
                                print("❌ Correcte Hofstede grafiek niet gevonden")
                                run.add_text("[Correcte Hofstede Gap-analyse grafiek - bestand niet gevonden]")
                        except Exception as e:
                            print(f"❌ Fout bij invoegen correcte Hofstede grafiek: {e}")
                        
                        break
                if any(run._element.xpath('.//a:blip') for run in doc.paragraphs[j].runs):
                    break
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_CORRECT_GAP_ANALYSIS.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_CORRECT_GAP_ANALYSIS.docx")
    print("✅ Correcte gap-analyse grafiek geplaatst")
    print("\n📊 De nieuwe grafiek toont:")
    print("   - Huidige situatie Euro Caps (oranje bars)")
    print("   - Gewenste situatie Six Sigma (groene bars)")
    print("   - Duidelijke gap-pijlen met waarden")
    print("   - Uitleg van alle dimensies en gaps")
    print("   - Professionele visualisatie van de verschillen")

if __name__ == "__main__":
    replace_gap_analysis_in_document()
