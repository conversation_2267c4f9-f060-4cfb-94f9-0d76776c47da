// Python Quiz Questions Database - Code-based Questions
const questionDatabase = {
    beginner: [
        {
            id: 1,
            type: "code-input",
            category: "Variabelen en Datatypes",
            question: "Maak drie variabelen: 'naam' (string), 'leeftijd' (integer), en 'student' (boolean). Print alle drie de variabelen uit.",
            expectedKeywords: ["naam", "leeftijd", "student", "print", "="],
            solution: `naam = "Jan"
leeftijd = 20
student = True
print(naam)
print(leeftijd)
print(student)`,
            explanation: {
                correct: "Uitstekend! Je hebt correct drie verschillende datatypes gebruikt: string (tekst tussen aanhalingstekens), integer (geheel getal), en boolean (True/False). Het print() statement toont de waarden.",
                incorrect: "Let op de datatypes: strings hebben aanhalingstekens, integers zijn getallen zonder aanhalingstekens, en booleans zijn True of False (met hoofdletter). Vergeet niet alle variabelen te printen.",
                hints: [
                    "Strings moeten tussen aanhalingstekens: naam = \"Jan\"",
                    "Integers zijn gewoon getallen: leeftijd = 20",
                    "Booleans zijn True of False: student = True",
                    "Gebruik print() om elke variabele te tonen"
                ]
            }
        },
        {
            id: 2,
            type: "code-input",
            category: "Rekenkundige Operatoren",
            question: "Schrijf code die twee getallen (a=15, b=4) gebruikt en alle rekenkundige operatoren toont: optelling, aftrekking, vermenigvuldiging, gewone deling, gehele deling, en modulo. Print elk resultaat met een beschrijving.",
            expectedKeywords: ["+", "-", "*", "/", "//", "%", "print"],
            solution: `a = 15
b = 4
print("Optelling:", a + b)
print("Aftrekking:", a - b)
print("Vermenigvuldiging:", a * b)
print("Gewone deling:", a / b)
print("Gehele deling:", a // b)
print("Modulo (rest):", a % b)`,
            explanation: {
                correct: "Perfect! Je hebt alle rekenkundige operatoren correct gebruikt. Let op het verschil tussen / (gewone deling, geeft float) en // (gehele deling, geeft integer). Modulo (%) geeft de rest van een deling.",
                incorrect: "Zorg ervoor dat je alle operatoren gebruikt: + (plus), - (min), * (keer), / (gedeeld door), // (gehele deling), % (modulo/rest). Print elk resultaat met een duidelijke beschrijving.",
                hints: [
                    "Gebruik a = 15 en b = 4 als variabelen",
                    "/ geeft een float resultaat (3.75)",
                    "// geeft alleen het gehele deel (3)",
                    "% geeft de rest van de deling (3)"
                ]
            }
        },
        {
            id: 3,
            type: "code-input",
            category: "Strings en String Methoden",
            question: "Maak een string variabele 'tekst' met de waarde 'python programmeren'. Gebruik string methoden om deze tekst te tonen in: hoofdletters, eerste letter van elk woord groot, en tel hoeveel keer de letter 'r' voorkomt.",
            expectedKeywords: ["upper", "title", "count", "print"],
            solution: `tekst = "python programmeren"
print("Hoofdletters:", tekst.upper())
print("Titel formaat:", tekst.title())
print("Aantal 'r':", tekst.count('r'))`,
            explanation: {
                correct: "Excellent! Je hebt de belangrijkste string methoden gebruikt: .upper() voor hoofdletters, .title() voor titel formaat (eerste letter van elk woord groot), en .count() om karakters te tellen.",
                incorrect: "Gebruik string methoden: .upper() voor hoofdletters, .title() voor titel formaat, en .count('r') om te tellen hoe vaak 'r' voorkomt. Vergeet niet de resultaten te printen.",
                hints: [
                    "tekst.upper() maakt alle letters groot",
                    "tekst.title() maakt eerste letter van elk woord groot",
                    "tekst.count('r') telt hoeveel keer 'r' voorkomt",
                    "Print elk resultaat met een beschrijving"
                ]
            }
        },
        {
            id: 4,
            type: "code-input",
            category: "Lists en Indexing",
            question: "Maak een lijst met 5 verschillende kleuren. Print de eerste kleur, de laatste kleur, en voeg een nieuwe kleur toe aan het einde van de lijst. Print daarna de hele lijst.",
            expectedKeywords: ["list", "[", "]", "print", "append", "0", "-1"],
            solution: `kleuren = ["rood", "blauw", "groen", "geel", "paars"]
print("Eerste kleur:", kleuren[0])
print("Laatste kleur:", kleuren[-1])
kleuren.append("oranje")
print("Hele lijst:", kleuren)`,
            explanation: {
                correct: "Perfect! Je hebt lists, indexing en de append() methode correct gebruikt. Index 0 is het eerste element, index -1 is het laatste element. append() voegt een element toe aan het einde.",
                incorrect: "Maak een lijst met vierkante haken [], gebruik index 0 voor het eerste element, index -1 voor het laatste element, en append() om een element toe te voegen.",
                hints: [
                    "Maak een lijst: kleuren = [\"rood\", \"blauw\", ...]",
                    "Eerste element: kleuren[0]",
                    "Laatste element: kleuren[-1]",
                    "Element toevoegen: kleuren.append(\"nieuwe_kleur\")"
                ]
            }
        },
        {
            id: 5,
            type: "code-input",
            category: "Loops en Range",
            question: "Schrijf een for-loop die de getallen 1 tot en met 10 print, maar sla de getallen 5 en 7 over (gebruik continue).",
            expectedKeywords: ["for", "range", "if", "continue", "print"],
            solution: `for i in range(1, 11):
    if i == 5 or i == 7:
        continue
    print(i)`,
            explanation: {
                correct: "Uitstekend! Je hebt range(1, 11) gebruikt voor getallen 1-10, en continue om bepaalde getallen over te slaan. Continue springt naar de volgende iteratie van de loop.",
                incorrect: "Gebruik range(1, 11) voor getallen 1-10, check met if of het getal 5 of 7 is, en gebruik continue om over te slaan.",
                hints: [
                    "range(1, 11) geeft getallen 1 tot en met 10",
                    "if i == 5 or i == 7: om te checken",
                    "continue springt naar volgende iteratie",
                    "print(i) om het getal te tonen"
                ]
            }
        },
        {
            id: 6,
            type: "code-input",
            category: "Functies en Parameters",
            question: "Schrijf een functie 'bereken_gemiddelde' die een lijst van getallen als parameter neemt en het gemiddelde teruggeeft. Test de functie met de lijst [10, 20, 30, 40, 50].",
            expectedKeywords: ["def", "bereken_gemiddelde", "sum", "len", "return", "print"],
            solution: `def bereken_gemiddelde(getallen):
    return sum(getallen) / len(getallen)

test_lijst = [10, 20, 30, 40, 50]
gemiddelde = bereken_gemiddelde(test_lijst)
print("Gemiddelde:", gemiddelde)`,
            explanation: {
                correct: "Perfect! Je hebt een functie gemaakt met def, sum() gebruikt om alle getallen op te tellen, len() voor het aantal elementen, en return om het resultaat terug te geven.",
                incorrect: "Definieer een functie met def, gebruik sum() en len() om het gemiddelde te berekenen, en return het resultaat. Test de functie door hem aan te roepen.",
                hints: [
                    "def bereken_gemiddelde(getallen): om functie te definiëren",
                    "sum(getallen) telt alle getallen op",
                    "len(getallen) geeft aantal elementen",
                    "return sum(getallen) / len(getallen)"
                ]
            }
        },
        {
            id: 7,
            type: "multiple-choice",
            category: "Conditionals",
            question: "Wat print de volgende code?",
            code: "x = 10\nif x > 5:\n    print('Groot')\nelse:\n    print('Klein')",
            options: {
                A: "Groot",
                B: "Klein",
                C: "10",
                D: "Niets"
            },
            correct: "A",
            explanation: "Omdat x (10) groter is dan 5, wordt 'Groot' geprint."
        },
        {
            id: 8,
            type: "multiple-choice",
            category: "Functions",
            question: "Hoe definieer je een functie in Python?",
            options: {
                A: "function mijn_functie():",
                B: "def mijn_functie():",
                C: "create mijn_functie():",
                D: "func mijn_functie():"
            },
            correct: "B",
            explanation: "In Python gebruik je het 'def' keyword om een functie te definiëren."
        },
        {
            id: 9,
            type: "code-input",
            category: "Functions",
            question: "Schrijf een functie 'kwadraat' die het kwadraat van een getal teruggeeft.",
            expectedOutput: "Functie moet x*x of x**2 returnen",
            hints: ["Gebruik def om de functie te definiëren", "Gebruik return om een waarde terug te geven"],
            solution: "def kwadraat(x):\n    return x ** 2"
        },
        {
            id: 10,
            type: "multiple-choice",
            category: "Modules",
            question: "Hoe importeer je de math module?",
            options: {
                A: "include math",
                B: "import math",
                C: "using math",
                D: "require math"
            },
            correct: "B",
            explanation: "In Python gebruik je 'import' om modules te importeren."
        }
    ],
    
    intermediate: [
        {
            id: 11,
            type: "multiple-choice",
            category: "Dictionaries",
            question: "Hoe krijg je alle keys van een dictionary?",
            code: "persoon = {'naam': 'Jan', 'leeftijd': 25}",
            options: {
                A: "persoon.keys()",
                B: "persoon.getKeys()",
                C: "persoon.allKeys()",
                D: "keys(persoon)"
            },
            correct: "A",
            explanation: "De .keys() methode geeft alle keys van een dictionary terug."
        },
        {
            id: 12,
            type: "code-input",
            category: "List Comprehensions",
            question: "Maak een list comprehension die de kwadraten van 1 tot 5 genereert.",
            expectedOutput: "[1, 4, 9, 16, 25]",
            hints: ["Gebruik [x**2 for x in range(...)]", "Range moet van 1 tot 6 gaan"],
            solution: "[x**2 for x in range(1, 6)]"
        },
        {
            id: 13,
            type: "multiple-choice",
            category: "Error Handling",
            question: "Welk keyword gebruik je om exceptions af te handelen?",
            options: {
                A: "catch",
                B: "except",
                C: "handle",
                D: "error"
            },
            correct: "B",
            explanation: "In Python gebruik je 'except' om exceptions af te handelen in een try-except blok."
        },
        {
            id: 14,
            type: "code-input",
            category: "File Handling",
            question: "Schrijf code om een bestand 'test.txt' te openen en de inhoud te lezen.",
            expectedOutput: "Code moet with open() gebruiken",
            hints: ["Gebruik with open('test.txt', 'r') as f:", "Gebruik f.read() om de inhoud te lezen"],
            solution: "with open('test.txt', 'r') as f:\n    inhoud = f.read()\n    print(inhoud)"
        },
        {
            id: 15,
            type: "multiple-choice",
            category: "OOP",
            question: "Welke methode wordt automatisch aangeroepen bij het maken van een object?",
            options: {
                A: "__create__()",
                B: "__new__()",
                C: "__init__()",
                D: "__start__()"
            },
            correct: "C",
            explanation: "De __init__ methode is de constructor die wordt aangeroepen bij het maken van een object."
        },
        {
            id: 16,
            type: "code-input",
            category: "Lambda Functions",
            question: "Maak een lambda functie die twee getallen optelt.",
            expectedOutput: "Lambda functie moet x + y returnen",
            hints: ["Gebruik lambda x, y: ...", "Lambda functies zijn anonieme functies"],
            solution: "lambda x, y: x + y"
        },
        {
            id: 17,
            type: "multiple-choice",
            category: "Modules",
            question: "Wat doet de volgende import statement?",
            code: "from math import sqrt",
            options: {
                A: "Importeert de hele math module",
                B: "Importeert alleen de sqrt functie",
                C: "Maakt een alias voor math",
                D: "Geeft een error"
            },
            correct: "B",
            explanation: "Deze import statement importeert alleen de sqrt functie uit de math module."
        },
        {
            id: 18,
            type: "code-input",
            category: "Loops",
            question: "Schrijf een while loop die telt van 1 tot 3.",
            expectedOutput: "1\n2\n3",
            hints: ["Start met i = 1", "Gebruik while i <= 3:", "Vergeet niet i += 1"],
            solution: "i = 1\nwhile i <= 3:\n    print(i)\n    i += 1"
        },
        {
            id: 19,
            type: "multiple-choice",
            category: "String Formatting",
            question: "Welke string formatting methode is het modernst in Python?",
            options: {
                A: "% formatting",
                B: ".format() methode",
                C: "f-strings",
                D: "Template strings"
            },
            correct: "C",
            explanation: "F-strings (f'...') zijn de modernste en meest efficiënte manier van string formatting in Python."
        },
        {
            id: 20,
            type: "code-input",
            category: "Functions",
            question: "Schrijf een functie met default parameter die 'Hallo' zegt tegen een naam (default: 'Wereld').",
            expectedOutput: "Functie moet default parameter hebben",
            hints: ["Gebruik def groet(naam='Wereld'):", "Return of print de groet"],
            solution: "def groet(naam='Wereld'):\n    return f'Hallo {naam}'"
        }
    ],
    
    advanced: [
        {
            id: 21,
            type: "multiple-choice",
            category: "Decorators",
            question: "Wat is een decorator in Python?",
            options: {
                A: "Een functie die andere functies modificeert",
                B: "Een manier om classes te maken",
                C: "Een type variabele",
                D: "Een import statement"
            },
            correct: "A",
            explanation: "Een decorator is een functie die andere functies kan modificeren of uitbreiden."
        },
        {
            id: 22,
            type: "code-input",
            category: "Generators",
            question: "Maak een generator functie die de eerste 3 even getallen genereert.",
            expectedOutput: "Generator moet 0, 2, 4 yielden",
            hints: ["Gebruik yield in plaats van return", "Even getallen zijn deelbaar door 2"],
            solution: "def even_getallen():\n    for i in range(0, 6, 2):\n        yield i"
        },
        {
            id: 23,
            type: "multiple-choice",
            category: "Context Managers",
            question: "Wat is het voordeel van 'with' statements?",
            options: {
                A: "Ze maken code sneller",
                B: "Ze zorgen voor automatische resource cleanup",
                C: "Ze maken code korter",
                D: "Ze voorkomen alle errors"
            },
            correct: "B",
            explanation: "With statements zorgen ervoor dat resources (zoals bestanden) automatisch worden vrijgegeven."
        },
        {
            id: 24,
            type: "code-input",
            category: "OOP Inheritance",
            question: "Maak een class 'Hond' die erft van 'Dier' en een 'blaf' methode heeft.",
            expectedOutput: "Class met inheritance en methode",
            hints: ["Gebruik class Hond(Dier):", "Definieer een blaf methode"],
            solution: "class Dier:\n    pass\n\nclass Hond(Dier):\n    def blaf(self):\n        return 'Woof!'"
        },
        {
            id: 25,
            type: "multiple-choice",
            category: "Regular Expressions",
            question: "Welke module gebruik je voor regular expressions?",
            options: {
                A: "regex",
                B: "re",
                C: "regexp",
                D: "pattern"
            },
            correct: "B",
            explanation: "De 're' module wordt gebruikt voor regular expressions in Python."
        },
        {
            id: 26,
            type: "multiple-choice",
            category: "Sets",
            question: "Wat is het verschil tussen een list en een set?",
            options: {
                A: "Sets kunnen duplicaten bevatten",
                B: "Lists zijn sneller",
                C: "Sets bevatten geen duplicaten en zijn ongeordend",
                D: "Er is geen verschil"
            },
            correct: "C",
            explanation: "Sets bevatten unieke elementen en hebben geen vaste volgorde, lists wel."
        },
        {
            id: 27,
            type: "code-input",
            category: "Exception Handling",
            question: "Schrijf een try-except blok dat een ZeroDivisionError afhandelt.",
            expectedOutput: "Try-except met ZeroDivisionError",
            hints: ["Gebruik try: en except ZeroDivisionError:", "Probeer een deling door nul"],
            solution: "try:\n    result = 10 / 0\nexcept ZeroDivisionError:\n    print('Kan niet delen door nul')"
        },
        {
            id: 28,
            type: "multiple-choice",
            category: "Modules",
            question: "Wat doet de volgende code?",
            code: "import random\nprint(random.randint(1, 10))",
            options: {
                A: "Print altijd 1",
                B: "Print altijd 10",
                C: "Print een willekeurig getal tussen 1 en 10",
                D: "Geeft een error"
            },
            correct: "C",
            explanation: "random.randint(1, 10) genereert een willekeurig geheel getal tussen 1 en 10 (inclusief)."
        },
        {
            id: 29,
            type: "code-input",
            category: "JSON",
            question: "Importeer json module en converteer een dictionary naar JSON string.",
            expectedOutput: "JSON string van dictionary",
            hints: ["Gebruik import json", "Gebruik json.dumps() om naar string te converteren"],
            solution: "import json\ndata = {'naam': 'Jan', 'leeftijd': 25}\njson_string = json.dumps(data)\nprint(json_string)"
        },
        {
            id: 30,
            type: "multiple-choice",
            category: "Tuples",
            question: "Wat is waar over tuples in Python?",
            options: {
                A: "Tuples zijn muteerbaar (kunnen worden gewijzigd)",
                B: "Tuples zijn immuteerbaar (kunnen niet worden gewijzigd)",
                C: "Tuples kunnen alleen getallen bevatten",
                D: "Tuples zijn hetzelfde als lists"
            },
            correct: "B",
            explanation: "Tuples zijn immuteerbaar, wat betekent dat ze niet kunnen worden gewijzigd na creatie."
        },
        {
            id: 31,
            type: "code-input",
            category: "Datetime",
            question: "Importeer datetime en print de huidige datum en tijd.",
            expectedOutput: "Huidige datum en tijd",
            hints: ["Gebruik import datetime", "Gebruik datetime.datetime.now()"],
            solution: "import datetime\nnu = datetime.datetime.now()\nprint(nu)"
        },
        {
            id: 32,
            type: "multiple-choice",
            category: "Boolean Logic",
            question: "Wat is het resultaat van: True and False or True?",
            options: {
                A: "True",
                B: "False",
                C: "Error",
                D: "None"
            },
            correct: "A",
            explanation: "Door operator precedence: (True and False) or True = False or True = True"
        },
        {
            id: 33,
            type: "code-input",
            category: "Enumerate",
            question: "Gebruik enumerate om door een lijst te itereren met index en waarde.",
            expectedOutput: "Index en waarde voor elk element",
            hints: ["Gebruik enumerate(lijst)", "Print zowel index als waarde"],
            solution: "lijst = ['a', 'b', 'c']\nfor index, waarde in enumerate(lijst):\n    print(f'{index}: {waarde}')"
        },
        {
            id: 34,
            type: "multiple-choice",
            category: "Slicing",
            question: "Wat doet lijst[1:3] als lijst = [0, 1, 2, 3, 4]?",
            options: {
                A: "[0, 1, 2]",
                B: "[1, 2]",
                C: "[1, 2, 3]",
                D: "[2, 3]"
            },
            correct: "B",
            explanation: "Slicing [1:3] geeft elementen van index 1 tot (maar niet inclusief) index 3: [1, 2]"
        },
        {
            id: 35,
            type: "code-input",
            category: "Zip Function",
            question: "Gebruik zip() om twee lijsten te combineren.",
            expectedOutput: "Gecombineerde lijsten",
            hints: ["Gebruik zip(lijst1, lijst2)", "Converteer naar list om resultaat te zien"],
            solution: "lijst1 = [1, 2, 3]\nlijst2 = ['a', 'b', 'c']\ngecombineerd = list(zip(lijst1, lijst2))\nprint(gecombineerd)"
        }
    ]
};

// Question categories for study recommendations
const categories = {
    "Variabelen": "Bestudeer datatypes, variabele toewijzing en type conversies",
    "Operatoren": "Oefen met rekenkundige, vergelijkings- en logische operatoren",
    "Strings": "Leer string methoden en string formatting",
    "Lists": "Bestudeer list methoden, indexing en slicing",
    "Loops": "Oefen met for en while loops, range() functie",
    "Conditionals": "Bestudeer if/elif/else statements en boolean logic",
    "Functions": "Leer functie definitie, parameters en return statements",
    "Modules": "Bestudeer import statements en standaard modules",
    "Dictionaries": "Leer dictionary methoden en key-value pairs",
    "List Comprehensions": "Oefen met list comprehensions en generator expressions",
    "Error Handling": "Bestudeer try/except/finally blokken",
    "File Handling": "Leer bestandsoperaties en context managers",
    "OOP": "Bestudeer classes, objects, inheritance en polymorphism",
    "Lambda Functions": "Oefen met anonieme functies en functional programming",
    "String Formatting": "Leer f-strings, .format() en % formatting",
    "Decorators": "Bestudeer decorator syntax en gebruik",
    "Generators": "Leer yield statements en generator functies",
    "Context Managers": "Bestudeer with statements en __enter__/__exit__",
    "OOP Inheritance": "Oefen met class inheritance en super()",
    "Regular Expressions": "Leer regex patterns en re module"
};

// Get questions for specific difficulty level
function getQuestions(difficulty) {
    const questions = questionDatabase[difficulty] || questionDatabase.beginner;
    return shuffleArray([...questions]); // Return shuffled copy
}

// Shuffle array utility function
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Get study recommendations based on wrong answers
function getStudyRecommendations(wrongCategories) {
    return wrongCategories.map(category => categories[category] || `Bestudeer meer over ${category}`);
}
