# 🐍 Python Examen Quiz Game

Een interactieve web-based quiz game om je voor te bereiden op je Python examen. Test je kennis van Python concepten met verschillende moeilijkheidsgraden en krijg directe feedback op je antwoorden.

## ✨ Features

- **Praktijkgerichte Code Vragen**: Focus op het schrijven van echte Python code
- **Geen Tijdslimiet**: <PERSON><PERSON>m alle tijd die je nodig hebt om te leren
- **Interactieve Code Editor**: Schrijf en test Python code direct in de browser
- **Uitgebreide Feedback**: Gedetailleerde uitleg waarom antwoorden goed of fout zijn
- **Hints Systeem**: Krijg hints wanneer je vastloopt
- **Oplossingen Tonen**: Bekijk de correcte oplossing met uitleg
- **Visuele Feedback**: Duidelijke indicatie of je antwoord correct is
- **Study Recommendations**: <PERSON><PERSON><PERSON><PERSON> gepersonaliseerde studieaanbevelingen
- **Review Mode**: <PERSON>ki<PERSON> al je antwoorden met uitgebreide uitleg
- **Responsive Design**: Werkt op desktop, tablet en mobiel

## 🎯 Quiz Onderwerpen

De quiz behandelt alle belangrijke Python concepten:

- **Basis Concepten**: Variabelen, datatypes, operatoren
- **Control Flow**: Loops, conditionals, boolean logic
- **Data Structures**: Lists, dictionaries, tuples, sets
- **Functions**: Functie definitie, parameters, return statements
- **Modules**: Import statements, standaard modules
- **Object-Oriented Programming**: Classes, inheritance, methods
- **Error Handling**: Try/except blokken, exception handling
- **File Handling**: Bestandsoperaties, context managers
- **Advanced Topics**: Decorators, generators, lambda functions

## 🚀 Hoe te Gebruiken

### Online (GitHub Pages)
1. Ga naar de live versie: [Python Quiz Game](https://jrvirus13.github.io/Americaps/Python-Quiz-Game/)
2. Kies je moeilijkheidsgraad
3. Beantwoord de vragen
4. Bekijk je resultaten en studieaanbevelingen

### Lokaal Draaien
1. Clone deze repository
2. Open `index.html` in je webbrowser
3. Begin met de quiz!

## 📁 Bestandsstructuur

```
Python-Quiz-Game/
├── index.html          # Hoofdpagina met quiz interface
├── styles.css          # CSS styling voor de quiz
├── script.js           # Hoofdfunctionaliteit van de quiz
├── questions.js        # Database met alle quiz vragen
└── README.md          # Deze documentatie
```

## 🎮 Hoe de Quiz Werkt

### Vraag Types
1. **Multiple Choice**: Kies het juiste antwoord uit 4 opties
2. **Code Input**: Schrijf Python code om een probleem op te lossen

### Scoring Systeem
- Elk correct antwoord geeft 1 punt
- Overgeslagen vragen geven 0 punten
- Foute antwoorden geven 0 punten
- Eindresultaat wordt getoond als percentage

### Timer
- Elke vraag heeft 30 seconden
- Timer wordt rood bij de laatste 10 seconden
- Automatisch overslaan als tijd op is

### Code Editor Features
- Syntax highlighting voor Python
- Tab indentatie ondersteuning
- Code uitvoeren en testen
- Basis Python simulatie voor feedback

## 📊 Resultaten & Analytics

Na het voltooien van de quiz krijg je:

- **Totale Score**: Aantal correcte antwoorden en percentage
- **Gedetailleerde Statistieken**: Correct, fout, overgeslagen
- **Tijd Analytics**: Gemiddelde tijd per vraag
- **Categorie Analyse**: Prestaties per onderwerp
- **Study Recommendations**: Gepersonaliseerde studieaanbevelingen
- **Answer Review**: Bekijk al je antwoorden met correcte oplossingen

## 🛠️ Technische Details

### Technologieën
- **HTML5**: Structuur en semantiek
- **CSS3**: Styling en responsive design
- **Vanilla JavaScript**: Functionaliteit en interactiviteit
- **CodeMirror**: Code editor component

### Browser Compatibiliteit
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Performance
- Geen externe dependencies (behalve CodeMirror CDN)
- Snelle laadtijden
- Offline functionaliteit na eerste load

## 📚 Quiz Content

De vragen zijn gebaseerd op:
- Python Basis Syllabus
- Praktische Python voorbeelden
- Veelvoorkomende examen onderwerpen
- Best practices in Python programmeren

### Moeilijkheidsgraden

**Beginner (10 vragen)**
- Basis syntax en datatypes
- Eenvoudige operaties
- Basis control flow

**Gemiddeld (15 vragen)**
- Functies en modules
- Data structures
- Error handling
- File operations

**Gevorderd (20 vragen)**
- Object-oriented programming
- Advanced Python features
- Complex problem solving
- Best practices

## 🎯 Studietips

1. **Begin met Beginner**: Ook als je ervaring hebt, start met het beginner niveau
2. **Oefen Code Schrijven**: Gebruik de code editor vragen om praktijk te krijgen
3. **Review je Fouten**: Bekijk altijd de review sectie na de quiz
4. **Volg Aanbevelingen**: Gebruik de studieaanbevelingen om je zwakke punten te verbeteren
5. **Herhaal Regelmatig**: Doe de quiz meerdere keren om je kennis te versterken

## 🤝 Bijdragen

Wil je bijdragen aan deze quiz? Je kunt:
- Nieuwe vragen toevoegen in `questions.js`
- Verbeteringen voorstellen voor de UI
- Bugs rapporteren
- Documentatie verbeteren

## 📄 Licentie

Dit project is gemaakt voor educatieve doeleinden. Voel je vrij om het te gebruiken en aan te passen voor je eigen studie.

## 🎓 Succes met je Examen!

Deze quiz is ontworpen om je te helpen slagen voor je Python examen. Veel succes met je studie en vergeet niet: oefening baart kunst! 🐍✨
