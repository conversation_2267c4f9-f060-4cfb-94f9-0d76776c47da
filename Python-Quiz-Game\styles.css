/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Screen Management */
.screen {
    display: none;
    animation: fadeIn 0.5s ease-in;
}

.screen.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Welcome Card */
.welcome-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.welcome-card h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 2rem;
}

.welcome-card ul {
    text-align: left;
    margin: 20px 0;
    padding-left: 20px;
}

.welcome-card li {
    margin: 8px 0;
    color: #666;
}

/* Difficulty Selection */
.difficulty-selection {
    margin-top: 30px;
}

.difficulty-selection h3 {
    margin-bottom: 20px;
    color: #4a5568;
}

.difficulty-btn {
    display: block;
    width: 100%;
    max-width: 300px;
    margin: 10px auto;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.difficulty-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Quiz Header */
.quiz-header {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.quiz-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.quiz-info span {
    font-weight: 600;
    color: #4a5568;
}

#progress-text {
    background: #e6fffa;
    color: #234e52;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-style: italic;
}

/* Question Card */
.question-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.question-card h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #2d3748;
    line-height: 1.6;
}

/* Code Block */
.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    overflow-x: auto;
}

/* Answer Options */
.answer-options {
    display: grid;
    gap: 15px;
    margin: 25px 0;
}

.option-btn {
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    background: #f7fafc;
    border-radius: 10px;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    line-height: 1.4;
}

.option-btn:hover {
    border-color: #667eea;
    background: #edf2f7;
    transform: translateX(5px);
}

.option-btn.selected {
    border-color: #667eea;
    background: #e6fffa;
    color: #234e52;
}

.option-btn.correct {
    border-color: #48bb78;
    background: #f0fff4;
    color: #22543d;
}

.option-btn.incorrect {
    border-color: #f56565;
    background: #fed7d7;
    color: #742a2a;
}

/* Code Input */
.code-input-container {
    margin: 25px 0;
}

.code-input-container label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #4a5568;
}

#code-editor {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    background: #1a202c;
    color: #e2e8f0;
}

.code-actions {
    display: flex;
    gap: 10px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.code-output {
    background: #1a202c;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    min-height: 60px;
    border: 2px solid #2d3748;
    white-space: pre-wrap;
    margin-top: 10px;
}

.code-output.error {
    border-color: #f56565;
    background: #fed7d7;
    color: #742a2a;
}

.code-output.success {
    border-color: #48bb78;
    background: #f0fff4;
    color: #22543d;
}

/* Action Buttons */
.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.action-btn.secondary:hover {
    background: #cbd5e0;
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.question-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    flex-wrap: wrap;
}

/* Results Screen */
.results-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
}

.results-card h2 {
    color: #4a5568;
    margin-bottom: 30px;
    font-size: 2.2rem;
}

.final-score {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
}

.score-total {
    font-size: 1rem;
    opacity: 0.8;
}

.score-percentage {
    font-size: 2.5rem;
    font-weight: bold;
    color: #4a5568;
}

/* Performance Analysis */
.performance-analysis {
    text-align: left;
    margin: 30px 0;
}

.performance-analysis h3 {
    color: #4a5568;
    margin-bottom: 15px;
    text-align: center;
}

.performance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.stat-label {
    color: #4a5568;
    font-weight: 500;
}

/* Recommendations */
.recommendations {
    text-align: left;
    margin: 30px 0;
}

.recommendations h3 {
    color: #4a5568;
    margin-bottom: 15px;
    text-align: center;
}

.recommendations ul {
    list-style: none;
    padding: 0;
}

.recommendations li {
    padding: 10px 15px;
    margin: 8px 0;
    background: #fff5f5;
    border-left: 4px solid #f56565;
    border-radius: 4px;
    color: #742a2a;
}

.results-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

/* Hints and Feedback */
.hints-container {
    background: #fff5f5;
    border: 2px solid #fed7d7;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.hints-container h4 {
    color: #c53030;
    margin-bottom: 10px;
}

.hints-container ul {
    margin: 0;
    padding-left: 20px;
}

.hints-container li {
    margin: 5px 0;
    color: #742a2a;
}

.feedback-container {
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 2px solid;
}

.feedback-container.correct {
    background: #f0fff4;
    border-color: #48bb78;
    color: #22543d;
}

.feedback-container.incorrect {
    background: #fed7d7;
    border-color: #f56565;
    color: #742a2a;
}

.feedback-container h4 {
    margin-bottom: 10px;
    font-weight: 600;
}

/* Review Screen */
.review-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.review-container h2 {
    color: #4a5568;
    margin-bottom: 25px;
    text-align: center;
}

.review-item {
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.review-item.correct {
    border-color: #48bb78;
    background: #f0fff4;
}

.review-item.incorrect {
    border-color: #f56565;
    background: #fed7d7;
}

.review-item.skipped {
    border-color: #ed8936;
    background: #fffaf0;
}

.review-question {
    font-weight: 600;
    margin-bottom: 10px;
    color: #2d3748;
}

.review-answer {
    margin: 5px 0;
    padding: 5px 10px;
    border-radius: 5px;
}

.review-answer.user {
    background: #edf2f7;
    color: #4a5568;
}

.review-answer.correct {
    background: #c6f6d5;
    color: #22543d;
}

/* Explanation Screen */
.explanation-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 900px;
    margin: 0 auto;
}

.explanation-container h2 {
    color: #4a5568;
    margin-bottom: 25px;
    text-align: center;
}

.explanation-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 8px;
}

.explanation-question {
    margin-bottom: 25px;
}

.explanation-question h3 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.explanation-solution {
    margin-bottom: 25px;
}

.explanation-solution h4 {
    color: #38a169;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.explanation-details {
    margin-bottom: 25px;
}

.explanation-details h4 {
    color: #3182ce;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.explanation-details div {
    background: #edf2f7;
    padding: 15px;
    border-radius: 8px;
    line-height: 1.6;
    color: #4a5568;
}

.explanation-concepts {
    margin-bottom: 25px;
}

.explanation-concepts h4 {
    color: #805ad5;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.explanation-concepts ul {
    background: #faf5ff;
    padding: 15px 15px 15px 35px;
    border-radius: 8px;
    border-left: 4px solid #805ad5;
}

.explanation-concepts li {
    margin: 8px 0;
    color: #553c9a;
    line-height: 1.5;
}

.explanation-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .welcome-card,
    .question-card,
    .results-card {
        padding: 20px;
    }
    
    .quiz-info {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .final-score {
        flex-direction: column;
    }
    
    .performance-stats {
        grid-template-columns: 1fr;
    }
    
    .results-actions,
    .question-actions,
    .code-actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
    }
}
