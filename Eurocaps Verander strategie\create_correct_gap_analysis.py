#!/usr/bin/env python3
"""
Script om correcte gap-analyse grafiek te maken
"""

import matplotlib.pyplot as plt
import numpy as np

def create_correct_gap_analysis():
    """Maakt correcte Hofstede gap-analyse grafiek met juiste verschillen"""
    
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Hofstede dimensies
    dimensions = ['Machtsafstand', 'Individualisme', 'Onzekerheids-\nvermijding', 
                  'Masculiniteit', 'Lange termijn\noriëntatie', 'Toegeeflijkheid']
    
    # CORRECTE SCORES - gebaseerd op Euro Caps analyse
    current_scores = [75, 55, 80, 60, 40, 35]  # Huidige situatie Euro Caps
    desired_scores = [50, 40, 60, 50, 65, 55]  # Gewenste situatie voor Six Sigma
    
    x = np.arange(len(dimensions))
    width = 0.35
    
    # Maak bars
    bars1 = ax.bar(x - width/2, current_scores, width, 
                   label='Huidige situatie Euro Caps', 
                   color='#FF6B35', alpha=0.8, edgecolor='black', linewidth=1.5)
    bars2 = ax.bar(x + width/2, desired_scores, width, 
                   label='Gewenste situatie Six Sigma', 
                   color='#2E8B57', alpha=0.8, edgecolor='black', linewidth=1.5)
    
    # Voeg waarden toe op de bars
    for i, bar in enumerate(bars1):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', 
                fontweight='bold', fontsize=11, color='black')
    
    for i, bar in enumerate(bars2):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', 
                fontweight='bold', fontsize=11, color='black')
    
    # Voeg gap-pijlen en gap-waarden toe
    for i, (current, desired) in enumerate(zip(current_scores, desired_scores)):
        gap = abs(current - desired)
        if gap > 0:  # Alleen als er een gap is
            # Bepaal pijl kleur en richting
            if current > desired:
                arrow_color = '#FF0000'  # Rood voor verlagen
                arrow_text = f'↓ {gap}'
                y_pos = max(current, desired) + 8
            else:
                arrow_color = '#00AA00'  # Groen voor verhogen
                arrow_text = f'↑ {gap}'
                y_pos = max(current, desired) + 8
            
            # Voeg gap tekst toe
            ax.text(i, y_pos, arrow_text, ha='center', va='center', 
                   fontweight='bold', fontsize=12, color=arrow_color,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', 
                            edgecolor=arrow_color, alpha=0.9))
    
    # Styling
    ax.set_xlabel('Hofstede Cultuurdimensies', fontweight='bold', fontsize=14)
    ax.set_ylabel('Score (0-100)', fontweight='bold', fontsize=14)
    ax.set_title('Gap-analyse: Hofstede Cultuurdimensies\nEuro Caps Huidige vs Gewenste Situatie voor Six Sigma', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(dimensions, rotation=0, ha='center', fontsize=12)
    ax.legend(loc='upper right', fontsize=12)
    ax.set_ylim(0, 100)
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    
    # Voeg tekstbox toe met uitleg van de gaps
    gap_text = """GAP-ANALYSE UITLEG:
    
Machtsafstand: 75 → 50 (↓25)
Meer participatie en empowerment nodig

Individualisme: 55 → 40 (↓15)  
Meer teamwork en samenwerking

Onzekerheidsvermijding: 80 → 60 (↓20)
Meer experimenteren en innovatie

Masculiniteit: 60 → 50 (↓10)
Meer balans tussen prestatie en welzijn

Lange termijn oriëntatie: 40 → 65 (↑25)
Focus op continue verbetering

Toegeeflijkheid: 35 → 55 (↑20)
Meer flexibiliteit en openheid"""
    
    props = dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8, edgecolor='navy')
    ax.text(0.02, 0.98, gap_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=props, fontfamily='monospace')
    
    # Voeg legenda toe voor pijlen
    arrow_legend = """↓ = Verlagen nodig (rood)
↑ = Verhogen nodig (groen)"""
    props2 = dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.8, edgecolor='orange')
    ax.text(0.98, 0.02, arrow_legend, transform=ax.transAxes, fontsize=10,
            verticalalignment='bottom', horizontalalignment='right', bbox=props2)
    
    plt.tight_layout()
    plt.savefig('hofstede_gap_analysis_correct.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    print("✅ Correcte Hofstede gap-analyse grafiek aangemaakt: hofstede_gap_analysis_correct.png")
    
    # Print de exacte gaps voor verificatie
    print("\n📊 GAP-ANALYSE VERIFICATIE:")
    for i, (dim, current, desired) in enumerate(zip(dimensions, current_scores, desired_scores)):
        gap = current - desired
        direction = "verlagen" if gap > 0 else "verhogen"
        print(f"{dim.replace(chr(10), ' ')}: {current} → {desired} (Gap: {abs(gap)}, {direction})")

if __name__ == "__main__":
    create_correct_gap_analysis()
