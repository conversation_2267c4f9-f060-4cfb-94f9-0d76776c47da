// Python Quiz Questions Database - Gebaseerd op Syllabus
const questionDatabase = {
    beginner: [
        {
            id: 1,
            type: "code-input",
            category: "Variabelen en Datatypes",
            question: "Maak variabelen voor: naam (string), leeftijd (integer), lengte (float), en is_student (boolean). Print alle variabelen.",
            expectedKeywords: ["naam", "leeftijd", "lengte", "is_student", "print", "="],
            solution: `naam = "Alice"
leeftijd = 30
lengte = 1.75
is_student = True
print(naam)
print(leeftijd)
print(lengte)
print(is_student)`,
            explanation: {
                correct: "Perfect! Je hebt alle basis datatypes gebruikt: string (tekst), integer (geheel getal), float (decimaal getal), en boolean (True/False).",
                incorrect: "Gebruik aanhalingstekens voor strings, gewone getallen voor integers, decimale getallen voor floats, en True/False voor booleans.",
                hints: [
                    "String: naam = \"Alice\"",
                    "Integer: leeftijd = 30",
                    "Float: lengte = 1.75",
                    "Boolean: is_student = True"
                ]
            }
        },
        {
            id: 2,
            type: "code-input",
            category: "Variabelen Wisselen",
            question: "Maak twee variabelen x=5 en y=10. Wissel hun waarden zonder een extra variabele te gebruiken. Print x en y voor en na het wisselen.",
            expectedKeywords: ["x", "y", "=", "print"],
            solution: `x = 5
y = 10
print("Voor wisselen: x =", x, "y =", y)
x, y = y, x
print("Na wisselen: x =", x, "y =", y)`,
            explanation: {
                correct: "Uitstekend! Je hebt de Python tuple unpacking gebruikt om variabelen te wisselen. Dit is een elegante manier zonder extra variabele.",
                incorrect: "Gebruik x, y = y, x om de waarden te wisselen. Dit is een speciale Python feature.",
                hints: [
                    "x = 5 en y = 10 om te beginnen",
                    "Print eerst de originele waarden",
                    "Gebruik x, y = y, x om te wisselen",
                    "Print daarna de nieuwe waarden"
                ]
            }
        },
        {
            id: 3,
            type: "code-input",
            category: "Print en Strings",
            question: "Print de tekst 'Hallo, wereld!' en gebruik daarna print() om je eigen naam en leeftijd te tonen in één zin.",
            expectedKeywords: ["print", "Hallo", "wereld"],
            solution: `print("Hallo, wereld!")
naam = "Alice"
leeftijd = 25
print("Mijn naam is", naam, "en ik ben", leeftijd, "jaar oud")`,
            explanation: {
                correct: "Goed gedaan! Je hebt print() gebruikt voor tekst en variabelen gecombineerd in één print statement.",
                incorrect: "Gebruik print() voor tekst tussen aanhalingstekens. Je kunt meerdere dingen printen door ze te scheiden met komma's.",
                hints: [
                    "print(\"Hallo, wereld!\") voor de eerste regel",
                    "Maak variabelen voor naam en leeftijd",
                    "Gebruik komma's om meerdere dingen te printen",
                    "print(\"tekst\", variabele, \"meer tekst\")"
                ]
            }
        },
        {
            id: 4,
            type: "code-input",
            category: "Rekenkundige Operatoren",
            question: "Maak twee variabelen a=15 en b=4. Bereken en print: optelling, aftrekking, vermenigvuldiging, gewone deling, gehele deling en modulo.",
            expectedKeywords: ["a", "b", "+", "-", "*", "/", "//", "%", "print"],
            solution: `a = 15
b = 4
print("Optelling:", a + b)
print("Aftrekking:", a - b)
print("Vermenigvuldiging:", a * b)
print("Gewone deling:", a / b)
print("Gehele deling:", a // b)
print("Modulo:", a % b)`,
            explanation: {
                correct: "Perfect! Je hebt alle rekenkundige operatoren gebruikt. Let op: / geeft een float, // geeft alleen het gehele deel, % geeft de rest.",
                incorrect: "Gebruik alle operatoren: + - * / // %. Let op het verschil tussen / (3.75) en // (3).",
                hints: [
                    "a = 15, b = 4",
                    "/ is gewone deling (3.75)",
                    "// is gehele deling (3)",
                    "% is modulo/rest (3)"
                ]
            }
        },
        {
            id: 5,
            type: "code-input",
            category: "Lists Basis",
            question: "Maak een lijst met getallen [1, 2, 3]. Print de lijst, voeg het getal 4 toe met append(), en print de lijst opnieuw.",
            expectedKeywords: ["list", "[", "]", "append", "print"],
            solution: `getallen = [1, 2, 3]
print("Originele lijst:", getallen)
getallen.append(4)
print("Na toevoegen:", getallen)`,
            explanation: {
                correct: "Goed! Je hebt een lijst gemaakt met vierkante haken en append() gebruikt om een element toe te voegen.",
                incorrect: "Maak een lijst met [1, 2, 3] en gebruik .append(4) om een element toe te voegen.",
                hints: [
                    "lijst = [1, 2, 3] om een lijst te maken",
                    "lijst.append(4) om 4 toe te voegen",
                    "Print de lijst voor en na append()"
                ]
            }
        },
        {
            id: 6,
            type: "code-input",
            category: "For Loop Basis",
            question: "Schrijf een for-loop die de getallen 1 tot en met 5 print. Gebruik range().",
            expectedKeywords: ["for", "range", "print"],
            solution: `for i in range(1, 6):
    print(i)`,
            explanation: {
                correct: "Perfect! Je hebt range(1, 6) gebruikt om getallen 1 tot en met 5 te krijgen. Let op: range(1, 6) geeft 1,2,3,4,5.",
                incorrect: "Gebruik for i in range(1, 6): en print(i) in de loop. Range eindigt altijd één voor het laatste getal.",
                hints: [
                    "for i in range(1, 6):",
                    "range(1, 6) geeft 1,2,3,4,5",
                    "Vergeet de dubbele punt :",
                    "print(i) met indentatie"
                ]
            }
        },
        {
            id: 7,
            type: "code-input",
            category: "If Statements",
            question: "Maak een variabele 'getal' met waarde 10. Schrijf een if-statement dat print 'Groot' als het getal groter is dan 5, anders print 'Klein'.",
            expectedKeywords: ["getal", "if", "else", "print", ">"],
            solution: `getal = 10
if getal > 5:
    print("Groot")
else:
    print("Klein")`,
            explanation: {
                correct: "Uitstekend! Je hebt een if-else statement gebruikt met een vergelijking. Let op de dubbele punt en indentatie.",
                incorrect: "Gebruik if getal > 5: gevolgd door print(\"Groot\"), dan else: gevolgd door print(\"Klein\"). Vergeet de indentatie niet.",
                hints: [
                    "getal = 10",
                    "if getal > 5:",
                    "    print(\"Groot\")",
                    "else:",
                    "    print(\"Klein\")"
                ]
            }
        },
        {
            id: 8,
            type: "code-input",
            category: "String Methoden",
            question: "Maak een string 'tekst' met waarde 'python'. Print de tekst in hoofdletters en tel hoeveel letters 'p' erin zitten.",
            expectedKeywords: ["tekst", "upper", "count", "print"],
            solution: `tekst = "python"
print("Hoofdletters:", tekst.upper())
print("Aantal p:", tekst.count('p'))`,
            explanation: {
                correct: "Goed gedaan! Je hebt .upper() gebruikt voor hoofdletters en .count() om karakters te tellen.",
                incorrect: "Gebruik .upper() om naar hoofdletters te converteren en .count('p') om te tellen.",
                hints: [
                    "tekst = \"python\"",
                    "tekst.upper() voor hoofdletters",
                    "tekst.count('p') om p's te tellen"
                ]
            }
        },
        {
            id: 9,
            type: "code-input",
            category: "Eenvoudige Functie",
            question: "Schrijf een functie 'groet' die geen parameters heeft en 'Hallo!' print. Roep de functie aan.",
            expectedKeywords: ["def", "groet", "print", "Hallo"],
            solution: `def groet():
    print("Hallo!")

groet()`,
            explanation: {
                correct: "Perfect! Je hebt een eenvoudige functie gemaakt met def, print gebruikt, en de functie aangeroepen.",
                incorrect: "Gebruik def groet(): om de functie te definiëren, print(\"Hallo!\") erin, en groet() om aan te roepen.",
                hints: [
                    "def groet(): om functie te definiëren",
                    "print(\"Hallo!\") in de functie",
                    "groet() om de functie aan te roepen"
                ]
            }
        },
        {
            id: 10,
            type: "code-input",
            category: "While Loop Basis",
            question: "Schrijf een while-loop die de getallen 1, 2, 3 print. Start met i=1 en stop als i groter is dan 3.",
            expectedKeywords: ["while", "i", "print", "<=", "+="],
            solution: `i = 1
while i <= 3:
    print(i)
    i += 1`,
            explanation: {
                correct: "Uitstekend! Je hebt een while-loop gebruikt met een teller variabele. Vergeet nooit i += 1 anders krijg je een oneindige loop!",
                incorrect: "Start met i = 1, gebruik while i <= 3:, print i, en verhoog i met i += 1.",
                hints: [
                    "i = 1 om te beginnen",
                    "while i <= 3: voor de conditie",
                    "print(i) om het getal te tonen",
                    "i += 1 om i te verhogen"
                ]
            }
        }
        {
            id: 8,
            type: "multiple-choice",
            category: "Functions",
            question: "Hoe definieer je een functie in Python?",
            options: {
                A: "function mijn_functie():",
                B: "def mijn_functie():",
                C: "create mijn_functie():",
                D: "func mijn_functie():"
            },
            correct: "B",
            explanation: "In Python gebruik je het 'def' keyword om een functie te definiëren."
        },
        {
            id: 9,
            type: "code-input",
            category: "Functions",
            question: "Schrijf een functie 'kwadraat' die het kwadraat van een getal teruggeeft.",
            expectedOutput: "Functie moet x*x of x**2 returnen",
            hints: ["Gebruik def om de functie te definiëren", "Gebruik return om een waarde terug te geven"],
            solution: "def kwadraat(x):\n    return x ** 2"
        },
        {
            id: 10,
            type: "multiple-choice",
            category: "Modules",
            question: "Hoe importeer je de math module?",
            options: {
                A: "include math",
                B: "import math",
                C: "using math",
                D: "require math"
            },
            correct: "B",
            explanation: "In Python gebruik je 'import' om modules te importeren."
        }
    ],
    
    intermediate: [
        {
            id: 11,
            type: "code-input",
            category: "Dictionary Basis",
            question: "Maak een dictionary 'persoon' met keys 'naam' en 'leeftijd'. Print beide waarden apart.",
            expectedKeywords: ["persoon", "{", "}", "naam", "leeftijd", "print"],
            solution: `persoon = {"naam": "Jan", "leeftijd": 25}
print("Naam:", persoon["naam"])
print("Leeftijd:", persoon["leeftijd"])`,
            explanation: {
                correct: "Perfect! Je hebt een dictionary gemaakt met accolades {} en waarden opgehaald met vierkante haken [].",
                incorrect: "Gebruik accolades {} voor dictionaries en vierkante haken [] om waarden op te halen.",
                hints: [
                    "persoon = {\"naam\": \"Jan\", \"leeftijd\": 25}",
                    "persoon[\"naam\"] om naam op te halen",
                    "persoon[\"leeftijd\"] om leeftijd op te halen"
                ]
            }
        },
        {
            id: 12,
            type: "code-input",
            category: "Functie met Parameters",
            question: "Schrijf een functie 'som' die twee parameters a en b neemt en hun som teruggeeft. Test met som(5, 3).",
            expectedKeywords: ["def", "som", "return", "+", "print"],
            solution: `def som(a, b):
    return a + b

resultaat = som(5, 3)
print("Som:", resultaat)`,
            explanation: {
                correct: "Uitstekend! Je hebt een functie met parameters gemaakt en return gebruikt om een waarde terug te geven.",
                incorrect: "Gebruik def som(a, b): om de functie te definiëren en return a + b om de som terug te geven.",
                hints: [
                    "def som(a, b): voor functie met parameters",
                    "return a + b om som terug te geven",
                    "som(5, 3) om functie aan te roepen"
                ]
            }
        },
        {
            id: 13,
            type: "code-input",
            category: "List Indexing",
            question: "Maak een lijst met namen ['Alice', 'Bob', 'Charlie']. Print de eerste naam en de laatste naam.",
            expectedKeywords: ["list", "[", "]", "print", "0", "-1"],
            solution: `namen = ["Alice", "Bob", "Charlie"]
print("Eerste naam:", namen[0])
print("Laatste naam:", namen[-1])`,
            explanation: {
                correct: "Perfect! Je hebt indexing gebruikt: [0] voor het eerste element en [-1] voor het laatste element.",
                incorrect: "Gebruik [0] voor het eerste element en [-1] voor het laatste element van een lijst.",
                hints: [
                    "namen = [\"Alice\", \"Bob\", \"Charlie\"]",
                    "namen[0] geeft het eerste element",
                    "namen[-1] geeft het laatste element"
                ]
            }
        },
        {
            id: 14,
            type: "code-input",
            category: "For Loop met List",
            question: "Maak een lijst met getallen [10, 20, 30]. Gebruik een for-loop om elk getal te printen.",
            expectedKeywords: ["for", "in", "print", "list"],
            solution: `getallen = [10, 20, 30]
for getal in getallen:
    print(getal)`,
            explanation: {
                correct: "Uitstekend! Je hebt een for-loop gebruikt om door een lijst te itereren. 'for item in lijst:' is de standaard manier.",
                incorrect: "Gebruik for getal in getallen: om door de lijst te gaan en print elk getal.",
                hints: [
                    "getallen = [10, 20, 30]",
                    "for getal in getallen:",
                    "    print(getal)"
                ]
            }
        },
        {
            id: 15,
            type: "code-input",
            category: "String Formatting",
            question: "Maak variabelen naam='Jan' en leeftijd=25. Print een zin: 'Mijn naam is Jan en ik ben 25 jaar oud' met f-string formatting.",
            expectedKeywords: ["f\"", "naam", "leeftijd", "print"],
            solution: `naam = "Jan"
leeftijd = 25
print(f"Mijn naam is {naam} en ik ben {leeftijd} jaar oud")`,
            explanation: {
                correct: "Perfect! Je hebt f-string formatting gebruikt. Dit is de moderne manier om variabelen in strings te gebruiken.",
                incorrect: "Gebruik f\"tekst {variabele} meer tekst\" voor f-string formatting.",
                hints: [
                    "naam = \"Jan\", leeftijd = 25",
                    "f\"Mijn naam is {naam} en ik ben {leeftijd} jaar oud\"",
                    "Gebruik accolades {} rond variabelen"
                ]
            }
        }
        {
            id: 15,
            type: "multiple-choice",
            category: "OOP",
            question: "Welke methode wordt automatisch aangeroepen bij het maken van een object?",
            options: {
                A: "__create__()",
                B: "__new__()",
                C: "__init__()",
                D: "__start__()"
            },
            correct: "C",
            explanation: "De __init__ methode is de constructor die wordt aangeroepen bij het maken van een object."
        },
        {
            id: 16,
            type: "code-input",
            category: "Lambda Functions",
            question: "Maak een lambda functie die twee getallen optelt.",
            expectedOutput: "Lambda functie moet x + y returnen",
            hints: ["Gebruik lambda x, y: ...", "Lambda functies zijn anonieme functies"],
            solution: "lambda x, y: x + y"
        },
        {
            id: 17,
            type: "multiple-choice",
            category: "Modules",
            question: "Wat doet de volgende import statement?",
            code: "from math import sqrt",
            options: {
                A: "Importeert de hele math module",
                B: "Importeert alleen de sqrt functie",
                C: "Maakt een alias voor math",
                D: "Geeft een error"
            },
            correct: "B",
            explanation: "Deze import statement importeert alleen de sqrt functie uit de math module."
        },
        {
            id: 18,
            type: "code-input",
            category: "Loops",
            question: "Schrijf een while loop die telt van 1 tot 3.",
            expectedOutput: "1\n2\n3",
            hints: ["Start met i = 1", "Gebruik while i <= 3:", "Vergeet niet i += 1"],
            solution: "i = 1\nwhile i <= 3:\n    print(i)\n    i += 1"
        },
        {
            id: 19,
            type: "multiple-choice",
            category: "String Formatting",
            question: "Welke string formatting methode is het modernst in Python?",
            options: {
                A: "% formatting",
                B: ".format() methode",
                C: "f-strings",
                D: "Template strings"
            },
            correct: "C",
            explanation: "F-strings (f'...') zijn de modernste en meest efficiënte manier van string formatting in Python."
        },
        {
            id: 20,
            type: "code-input",
            category: "Functions",
            question: "Schrijf een functie met default parameter die 'Hallo' zegt tegen een naam (default: 'Wereld').",
            expectedOutput: "Functie moet default parameter hebben",
            hints: ["Gebruik def groet(naam='Wereld'):", "Return of print de groet"],
            solution: "def groet(naam='Wereld'):\n    return f'Hallo {naam}'"
        }
    ],
    
    advanced: [
        {
            id: 21,
            type: "code-input",
            category: "Try-Except Basis",
            question: "Schrijf een try-except blok dat probeert 10 te delen door 0. Print 'Fout!' als er een error is.",
            expectedKeywords: ["try", "except", "print", "/"],
            solution: `try:
    resultaat = 10 / 0
    print(resultaat)
except:
    print("Fout!")`,
            explanation: {
                correct: "Goed! Je hebt try-except gebruikt om errors af te handelen. Dit voorkomt dat je programma crasht.",
                incorrect: "Gebruik try: voor de code die een fout kan geven, en except: om de fout af te handelen.",
                hints: [
                    "try: om code te proberen",
                    "10 / 0 geeft een ZeroDivisionError",
                    "except: om de fout af te handelen",
                    "print(\"Fout!\") in het except blok"
                ]
            }
        },
        {
            id: 22,
            type: "code-input",
            category: "Import Module",
            question: "Importeer de math module en gebruik math.sqrt() om de wortel van 16 te berekenen. Print het resultaat.",
            expectedKeywords: ["import", "math", "sqrt", "print"],
            solution: `import math
resultaat = math.sqrt(16)
print("Wortel van 16:", resultaat)`,
            explanation: {
                correct: "Perfect! Je hebt een module geïmporteerd en een functie ervan gebruikt. math.sqrt() berekent de vierkantswortel.",
                incorrect: "Gebruik import math om de module te importeren en math.sqrt(16) om de wortel te berekenen.",
                hints: [
                    "import math om de module te importeren",
                    "math.sqrt(16) om wortel te berekenen",
                    "Print het resultaat"
                ]
            }
        },
        {
            id: 23,
            type: "code-input",
            category: "Dictionary Methods",
            question: "Maak een dictionary student = {'naam': 'Lisa', 'cijfer': 8}. Print alle keys en alle values apart.",
            expectedKeywords: ["dictionary", "{", "}", "keys", "values", "print"],
            solution: `student = {"naam": "Lisa", "cijfer": 8}
print("Keys:", list(student.keys()))
print("Values:", list(student.values()))`,
            explanation: {
                correct: "Uitstekend! Je hebt .keys() en .values() gebruikt om alle keys en values van een dictionary te krijgen.",
                incorrect: "Gebruik .keys() om alle keys te krijgen en .values() om alle values te krijgen.",
                hints: [
                    "student = {\"naam\": \"Lisa\", \"cijfer\": 8}",
                    "student.keys() geeft alle keys",
                    "student.values() geeft alle values",
                    "list() om het leesbaar te maken"
                ]
            }
        },
        {
            id: 24,
            type: "code-input",
            category: "List Slicing",
            question: "Maak een lijst getallen = [1, 2, 3, 4, 5]. Print de eerste 3 elementen en de laatste 2 elementen met slicing.",
            expectedKeywords: ["list", "[", "]", ":", "print"],
            solution: `getallen = [1, 2, 3, 4, 5]
print("Eerste 3:", getallen[:3])
print("Laatste 2:", getallen[-2:])`,
            explanation: {
                correct: "Perfect! Je hebt slicing gebruikt: [:3] voor eerste 3 elementen en [-2:] voor laatste 2 elementen.",
                incorrect: "Gebruik [:3] voor de eerste 3 elementen en [-2:] voor de laatste 2 elementen.",
                hints: [
                    "getallen = [1, 2, 3, 4, 5]",
                    "getallen[:3] geeft eerste 3 elementen",
                    "getallen[-2:] geeft laatste 2 elementen"
                ]
            }
        },
        {
            id: 25,
            type: "code-input",
            category: "Eenvoudige Class",
            question: "Maak een eenvoudige class 'Auto' met een methode 'start' die 'Auto gestart!' print. Maak een object en roep de methode aan.",
            expectedKeywords: ["class", "Auto", "def", "start", "print"],
            solution: `class Auto:
    def start(self):
        print("Auto gestart!")

mijn_auto = Auto()
mijn_auto.start()`,
            explanation: {
                correct: "Uitstekend! Je hebt een class gemaakt, een methode gedefinieerd, een object gemaakt en de methode aangeroepen.",
                incorrect: "Gebruik class Auto: om een class te maken, def start(self): voor een methode, en maak een object met Auto().",
                hints: [
                    "class Auto: om class te definiëren",
                    "def start(self): voor methode",
                    "mijn_auto = Auto() om object te maken",
                    "mijn_auto.start() om methode aan te roepen"
                ]
            }
        },
        {
            id: 26,
            type: "multiple-choice",
            category: "Sets",
            question: "Wat is het verschil tussen een list en een set?",
            options: {
                A: "Sets kunnen duplicaten bevatten",
                B: "Lists zijn sneller",
                C: "Sets bevatten geen duplicaten en zijn ongeordend",
                D: "Er is geen verschil"
            },
            correct: "C",
            explanation: "Sets bevatten unieke elementen en hebben geen vaste volgorde, lists wel."
        },
        {
            id: 27,
            type: "code-input",
            category: "Exception Handling",
            question: "Schrijf een try-except blok dat een ZeroDivisionError afhandelt.",
            expectedOutput: "Try-except met ZeroDivisionError",
            hints: ["Gebruik try: en except ZeroDivisionError:", "Probeer een deling door nul"],
            solution: "try:\n    result = 10 / 0\nexcept ZeroDivisionError:\n    print('Kan niet delen door nul')"
        },
        {
            id: 28,
            type: "multiple-choice",
            category: "Modules",
            question: "Wat doet de volgende code?",
            code: "import random\nprint(random.randint(1, 10))",
            options: {
                A: "Print altijd 1",
                B: "Print altijd 10",
                C: "Print een willekeurig getal tussen 1 en 10",
                D: "Geeft een error"
            },
            correct: "C",
            explanation: "random.randint(1, 10) genereert een willekeurig geheel getal tussen 1 en 10 (inclusief)."
        },
        {
            id: 29,
            type: "code-input",
            category: "JSON",
            question: "Importeer json module en converteer een dictionary naar JSON string.",
            expectedOutput: "JSON string van dictionary",
            hints: ["Gebruik import json", "Gebruik json.dumps() om naar string te converteren"],
            solution: "import json\ndata = {'naam': 'Jan', 'leeftijd': 25}\njson_string = json.dumps(data)\nprint(json_string)"
        },
        {
            id: 30,
            type: "multiple-choice",
            category: "Tuples",
            question: "Wat is waar over tuples in Python?",
            options: {
                A: "Tuples zijn muteerbaar (kunnen worden gewijzigd)",
                B: "Tuples zijn immuteerbaar (kunnen niet worden gewijzigd)",
                C: "Tuples kunnen alleen getallen bevatten",
                D: "Tuples zijn hetzelfde als lists"
            },
            correct: "B",
            explanation: "Tuples zijn immuteerbaar, wat betekent dat ze niet kunnen worden gewijzigd na creatie."
        },
        {
            id: 31,
            type: "code-input",
            category: "Datetime",
            question: "Importeer datetime en print de huidige datum en tijd.",
            expectedOutput: "Huidige datum en tijd",
            hints: ["Gebruik import datetime", "Gebruik datetime.datetime.now()"],
            solution: "import datetime\nnu = datetime.datetime.now()\nprint(nu)"
        },
        {
            id: 32,
            type: "multiple-choice",
            category: "Boolean Logic",
            question: "Wat is het resultaat van: True and False or True?",
            options: {
                A: "True",
                B: "False",
                C: "Error",
                D: "None"
            },
            correct: "A",
            explanation: "Door operator precedence: (True and False) or True = False or True = True"
        },
        {
            id: 33,
            type: "code-input",
            category: "Enumerate",
            question: "Gebruik enumerate om door een lijst te itereren met index en waarde.",
            expectedOutput: "Index en waarde voor elk element",
            hints: ["Gebruik enumerate(lijst)", "Print zowel index als waarde"],
            solution: "lijst = ['a', 'b', 'c']\nfor index, waarde in enumerate(lijst):\n    print(f'{index}: {waarde}')"
        },
        {
            id: 34,
            type: "multiple-choice",
            category: "Slicing",
            question: "Wat doet lijst[1:3] als lijst = [0, 1, 2, 3, 4]?",
            options: {
                A: "[0, 1, 2]",
                B: "[1, 2]",
                C: "[1, 2, 3]",
                D: "[2, 3]"
            },
            correct: "B",
            explanation: "Slicing [1:3] geeft elementen van index 1 tot (maar niet inclusief) index 3: [1, 2]"
        },
        {
            id: 35,
            type: "code-input",
            category: "Zip Function",
            question: "Gebruik zip() om twee lijsten te combineren.",
            expectedOutput: "Gecombineerde lijsten",
            hints: ["Gebruik zip(lijst1, lijst2)", "Converteer naar list om resultaat te zien"],
            solution: "lijst1 = [1, 2, 3]\nlijst2 = ['a', 'b', 'c']\ngecombineerd = list(zip(lijst1, lijst2))\nprint(gecombineerd)"
        }
    ]
};

// Question categories for study recommendations
const categories = {
    "Variabelen": "Bestudeer datatypes, variabele toewijzing en type conversies",
    "Operatoren": "Oefen met rekenkundige, vergelijkings- en logische operatoren",
    "Strings": "Leer string methoden en string formatting",
    "Lists": "Bestudeer list methoden, indexing en slicing",
    "Loops": "Oefen met for en while loops, range() functie",
    "Conditionals": "Bestudeer if/elif/else statements en boolean logic",
    "Functions": "Leer functie definitie, parameters en return statements",
    "Modules": "Bestudeer import statements en standaard modules",
    "Dictionaries": "Leer dictionary methoden en key-value pairs",
    "List Comprehensions": "Oefen met list comprehensions en generator expressions",
    "Error Handling": "Bestudeer try/except/finally blokken",
    "File Handling": "Leer bestandsoperaties en context managers",
    "OOP": "Bestudeer classes, objects, inheritance en polymorphism",
    "Lambda Functions": "Oefen met anonieme functies en functional programming",
    "String Formatting": "Leer f-strings, .format() en % formatting",
    "Decorators": "Bestudeer decorator syntax en gebruik",
    "Generators": "Leer yield statements en generator functies",
    "Context Managers": "Bestudeer with statements en __enter__/__exit__",
    "OOP Inheritance": "Oefen met class inheritance en super()",
    "Regular Expressions": "Leer regex patterns en re module"
};

// Get questions for specific difficulty level
function getQuestions(difficulty) {
    const questions = questionDatabase[difficulty] || questionDatabase.beginner;
    return shuffleArray([...questions]); // Return shuffled copy
}

// Shuffle array utility function
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Get study recommendations based on wrong answers
function getStudyRecommendations(wrongCategories) {
    return wrongCategories.map(category => categories[category] || `Bestudeer meer over ${category}`);
}
