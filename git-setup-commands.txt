# Git Setup Commands voor Python Quiz Game

# 1. Navigeer naar je project folder
cd "C:\Users\<USER>\Documents\augment-projects\Americaps"

# 2. Initialiseer Git repository (als nog niet gedaan)
git init

# 3. Voeg alle bestanden toe
git add .

# 4. <PERSON><PERSON> <PERSON><PERSON> commit
git commit -m "Add Python Quiz Game"

# 5. Voeg GitHub repository toe als remote
# Vervang 'jrvirus13' met je GitHub username en 'Americaps' met je repository naam
git remote add origin https://github.com/jrvirus13/Americaps.git

# 6. Push naar GitHub
git branch -M main
git push -u origin main

# Als je problemen hebt met authenticatie, gebruik dan:
# git config --global user.name "jrvirus13"
# git config --global user.email "<EMAIL>"
