#!/usr/bin/env python3
"""
<PERSON>ript om echte visuals en diepgaande analyses te maken voor het adviesrapport
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.patches import Rectangle, FancyBboxPatch, Circle
import seaborn as sns
from docx import Document
from docx.shared import Inches
import os

# Set style
plt.style.use('default')
sns.set_palette("husl")

def create_kotter_phases_model():
    """Maakt een eigen Kotter model in fasen gebaseerd op onderzoek"""
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # Kleuren voor de fasen
    colors = {
        'fase1': '#FF6B6B',  # Rood voor voorbereiding
        'fase2': '#4ECDC4',  # Turquoise voor implementatie  
        'fase3': '#45B7D1',  # Blauw voor verankering
        'text': '#2C3E50'    # Donkerblauw voor tekst
    }
    
    # Fase 1: Voorbereiding (Stappen 1-3)
    fase1_box = FancyBboxPatch((0.5, 7), 13, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['fase1'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(fase1_box)
    
    ax.text(7, 8.7, 'FASE 1: VOORBEREIDING (3 maanden)', 
            ha='center', va='center', fontsize=16, fontweight='bold', color='white')
    ax.text(7, 8.2, 'Kotter Stappen 1-3', 
            ha='center', va='center', fontsize=12, color='white')
    ax.text(7, 7.7, '• Urgentiebesef creëren\n• Leidende coalitie vormen\n• Visie en strategie ontwikkelen', 
            ha='center', va='center', fontsize=10, color='white')
    
    # Fase 2: Implementatie (Stappen 4-6)
    fase2_box = FancyBboxPatch((0.5, 4), 13, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['fase2'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(fase2_box)
    
    ax.text(7, 5.7, 'FASE 2: IMPLEMENTATIE (12 maanden)', 
            ha='center', va='center', fontsize=16, fontweight='bold', color='white')
    ax.text(7, 5.2, 'Kotter Stappen 4-6', 
            ha='center', va='center', fontsize=12, color='white')
    ax.text(7, 4.7, '• Visie communiceren\n• Medewerkers empoweren\n• Korte-termijn successen genereren', 
            ha='center', va='center', fontsize=10, color='white')
    
    # Fase 3: Verankering (Stappen 7-8)
    fase3_box = FancyBboxPatch((0.5, 1), 13, 2.5, 
                               boxstyle="round,pad=0.1", 
                               facecolor=colors['fase3'], 
                               edgecolor='black', linewidth=2)
    ax.add_patch(fase3_box)
    
    ax.text(7, 2.7, 'FASE 3: VERANKERING (6 maanden)', 
            ha='center', va='center', fontsize=16, fontweight='bold', color='white')
    ax.text(7, 2.2, 'Kotter Stappen 7-8', 
            ha='center', va='center', fontsize=12, color='white')
    ax.text(7, 1.7, '• Verbeteringen consolideren\n• Nieuwe aanpak verankeren in cultuur', 
            ha='center', va='center', fontsize=10, color='white')
    
    # Pijlen tussen fasen
    ax.arrow(7, 6.8, 0, -0.1, head_width=0.3, head_length=0.1, fc='black', ec='black')
    ax.arrow(7, 3.8, 0, -0.1, head_width=0.3, head_length=0.1, fc='black', ec='black')
    
    # Titel
    ax.text(7, 10.5, 'Kotter\'s 8-Stappenmodel in Drie Fasen voor Euro Caps', 
            ha='center', va='center', fontsize=18, fontweight='bold', color=colors['text'])
    
    # Six Sigma integratie
    ax.text(7, 0.2, 'Geïntegreerd met Six Sigma DMAIC-cyclus gedurende alle fasen', 
            ha='center', va='center', fontsize=12, style='italic', color=colors['text'])
    
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 11)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('kotter_fasen_model_euro_caps.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Kotter fasen model aangemaakt: kotter_fasen_model_euro_caps.png")

def create_hofstede_analysis():
    """Maakt Hofstede analyse voor Euro Caps"""
    
    # Data voor Euro Caps cultuur analyse
    dimensies = ['Machtsafstand', 'Individualisme', 'Masculiniteit', 
                'Onzekerheidsvermijding', 'Langetermijn\noriëntatie', 'Toegeeflijkheid']
    
    huidige_scores = [35, 45, 40, 65, 70, 60]  # Huidige situatie Euro Caps
    gewenste_scores = [30, 50, 35, 55, 75, 65]  # Gewenste situatie
    
    x = np.arange(len(dimensies))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    bars1 = ax.bar(x - width/2, huidige_scores, width, label='Huidige situatie', 
                   color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, gewenste_scores, width, label='Gewenste situatie', 
                   color='#4ECDC4', alpha=0.8)
    
    ax.set_xlabel('Hofstede Cultuurdimensies', fontsize=12, fontweight='bold')
    ax.set_ylabel('Score (0-100)', fontsize=12, fontweight='bold')
    ax.set_title('Hofstede Cultuuranalyse Euro Caps:\nHuidige vs Gewenste Situatie', 
                 fontsize=14, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(dimensies, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 100)
    
    # Voeg waarden toe op de balken
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontweight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('hofstede_analyse_euro_caps.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Hofstede analyse aangemaakt: hofstede_analyse_euro_caps.png")

def create_stakeholder_matrix():
    """Maakt stakeholder power/interest matrix"""
    
    # Stakeholder data
    stakeholders = {
        'Nils Clement (CEO)': (9, 9),
        'Servé Bosland': (8, 9),
        'Erik Dekker (ICT)': (7, 8),
        'Niene Tepe': (6, 9),
        'Berkan Arrindell': (8, 8),
        'Maik Ritter': (7, 6),
        'Maria Stanić': (7, 6),
        'Rijk Wegen': (6, 7),
        'Ko Jager': (5, 5),
        'Uwe Regel (HR)': (4, 5),
        'Kees Keurig': (5, 6),
        'Ismail Berenschot': (3, 4),
        'Tila Karren': (4, 5)
    }
    
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # Kwadrant kleuren
    ax.axhspan(0, 5, 0, 5, alpha=0.2, color='red', label='Monitor')
    ax.axhspan(5, 10, 0, 5, alpha=0.2, color='orange', label='Keep Satisfied')
    ax.axhspan(0, 5, 5, 10, alpha=0.2, color='yellow', label='Keep Informed')
    ax.axhspan(5, 10, 5, 10, alpha=0.2, color='green', label='Manage Closely')
    
    # Plot stakeholders
    for name, (power, interest) in stakeholders.items():
        ax.scatter(interest, power, s=200, alpha=0.7, 
                  c='darkblue', edgecolors='black', linewidth=2)
        ax.annotate(name, (interest, power), xytext=(5, 5), 
                   textcoords='offset points', fontsize=9, 
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    ax.set_xlabel('Interest (Belang)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Power (Macht)', fontsize=12, fontweight='bold')
    ax.set_title('Stakeholder Power/Interest Matrix Euro Caps', 
                 fontsize=14, fontweight='bold', pad=20)
    
    # Grid en labels
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    
    # Kwadrant labels
    ax.text(2.5, 2.5, 'MONITOR\n(Lage prioriteit)', ha='center', va='center', 
            fontsize=10, fontweight='bold', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    ax.text(7.5, 2.5, 'KEEP INFORMED\n(Medium prioriteit)', ha='center', va='center', 
            fontsize=10, fontweight='bold', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    ax.text(2.5, 7.5, 'KEEP SATISFIED\n(Medium prioriteit)', ha='center', va='center', 
            fontsize=10, fontweight='bold', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    ax.text(7.5, 7.5, 'MANAGE CLOSELY\n(Hoge prioriteit)', ha='center', va='center', 
            fontsize=10, fontweight='bold', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('stakeholder_matrix_euro_caps.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Stakeholder matrix aangemaakt: stakeholder_matrix_euro_caps.png")

def create_gap_analysis():
    """Maakt gap analyse visualisatie"""
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Huidige en gewenste situatie
    categories = ['Organisatie\nstructuur', 'Communicatie', 'Leiderschaps\nstijl', 
                 'Medewerker\nbetrokkenheid', 'Proces\nefficiëntie', 'Innovatie\ncultuur']
    
    huidige_scores = [6, 5, 6, 5, 7, 5]
    gewenste_scores = [8, 8, 8, 8, 9, 8]
    gaps = [g - h for g, h in zip(gewenste_scores, huidige_scores)]
    
    x = np.arange(len(categories))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, huidige_scores, width, label='Huidige situatie', 
                   color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x + width/2, gewenste_scores, width, label='Gewenste situatie', 
                   color='#4ECDC4', alpha=0.8)
    
    # Gap pijlen
    for i, gap in enumerate(gaps):
        if gap > 0:
            ax.annotate('', xy=(i, gewenste_scores[i]), xytext=(i, huidige_scores[i]),
                       arrowprops=dict(arrowstyle='<->', color='red', lw=2))
            ax.text(i, (huidige_scores[i] + gewenste_scores[i])/2, f'Gap: {gap}',
                   ha='center', va='center', fontweight='bold', 
                   bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
    
    ax.set_xlabel('Organisatie Aspecten', fontsize=12, fontweight='bold')
    ax.set_ylabel('Score (1-10)', fontsize=12, fontweight='bold')
    ax.set_title('Gap Analyse Euro Caps: Huidige vs Gewenste Situatie', 
                 fontsize=14, fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 10)
    
    plt.tight_layout()
    plt.savefig('gap_analyse_euro_caps.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Gap analyse aangemaakt: gap_analyse_euro_caps.png")

def create_change_curve():
    """Maakt Kübler-Ross verandercurve"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Curve data
    x = np.linspace(0, 10, 100)
    y = -2 * np.exp(-0.5 * (x - 2)**2) + 1.5 * np.exp(-0.3 * (x - 7)**2) + 0.5
    
    ax.plot(x, y, linewidth=3, color='#2C3E50')
    
    # Fasen markeren
    fasen = ['Ontkenning', 'Woede', 'Onderhandeling', 'Depressie', 'Acceptatie']
    x_fasen = [1, 2.5, 4, 5.5, 8]
    y_fasen = [-1.5, -1.8, -0.5, -1.2, 1.2]
    
    colors = ['red', 'orange', 'yellow', 'blue', 'green']
    
    for i, (fase, x_pos, y_pos, color) in enumerate(zip(fasen, x_fasen, y_fasen, colors)):
        ax.scatter(x_pos, y_pos, s=200, c=color, alpha=0.7, edgecolors='black', linewidth=2)
        ax.annotate(fase, (x_pos, y_pos), xytext=(0, 20), 
                   textcoords='offset points', ha='center', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7))
    
    ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax.set_xlabel('Tijd (maanden)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Emotioneel Welzijn', fontsize=12, fontweight='bold')
    ax.set_title('Kübler-Ross Verandercurve voor Euro Caps Medewerkers', 
                 fontsize=14, fontweight='bold', pad=20)
    
    # Y-as labels
    ax.set_yticks([-2, -1, 0, 1, 2])
    ax.set_yticklabels(['Zeer Negatief', 'Negatief', 'Neutraal', 'Positief', 'Zeer Positief'])
    
    ax.grid(True, alpha=0.3)
    ax.set_xlim(0, 10)
    ax.set_ylim(-2.5, 2)
    
    plt.tight_layout()
    plt.savefig('kubler_ross_curve_euro_caps.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Kübler-Ross curve aangemaakt: kubler_ross_curve_euro_caps.png")

if __name__ == "__main__":
    print("Aanmaken van echte visuals en analyses...")
    create_kotter_phases_model()
    create_hofstede_analysis()
    create_stakeholder_matrix()
    create_gap_analysis()
    create_change_curve()
    print("Alle visuals aangemaakt!")
